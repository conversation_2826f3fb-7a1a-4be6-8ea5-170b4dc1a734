// 创建100条假数据
const arr = new Array(10).form().map((item) => ({
  color: 'red',
  workstationCode: 1,
}));

console.log(arr);

// 函数防抖
function debounce(fn, delay) {
  let timer = null;
  return function () {
    clearTimeout(timer);
    timer = setTimeout(() => fn.apply(this, arguments), delay);
  };
}

// 函数节流 添加立即执行参数
function throttle(fn, delay, immediate = false) {
  let timer = null;
  return function () {
    if (timer) return;
    if (immediate) {
      fn.apply(this, arguments);
      timer = setTimeout(() => {
        timer = null;
      }, delay);
    } else {
      timer = setTimeout(() => fn.apply(this, arguments), delay);
    }
  };
}

